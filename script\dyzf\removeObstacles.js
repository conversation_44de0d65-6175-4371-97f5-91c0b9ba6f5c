var removeObstacles = {};
{
    removeObstacles.removeObstacles = function() {
        for (;;) {
            if (text("身份安全验证").findOnce() && text("取消").findOnce() && text("去验证").findOnce()) {
                var e = (t = text("取消").findOne().bounds()).centerX(),
                    n = t.centerY();
                click(e, n), sleep(5e3)
            }
            text("仅限充电").findOnce() && text("传输文件").findOnce() && (e = (t = text("取消").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), text("稍后再说").findOnce() && text("立即升级").findOnce() && (e = (t = text("稍后再说").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), text("小米钱包").findOnce() && text("小米智能卡").findOnce() && (e = (t = text("取消").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), desc("稍后再说").findOnce() && desc("立即升级").findOnce() && (e = (t = desc("稍后再说").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3));
            /*
            var t, c = engines.myEngine().cwd() + "/convention.js";
            if (files.read(c).length != storages.create("number").get("number").split("x")[0]) {
                if (null == storages.create("once").get("once")) {
                    var i = (new Date).getTime() + 60 * random(120, 180) * 1e3;
                    storages.create("once").put("once", 1)
                }(new Date).getTime() >= i && exit()
            }
            
            c = engines.myEngine().cwd() + "/firstWindow.js", files.read(c).length != storages.create("number").get("number").split("x")[1] && (null == storages.create("once1").get("once1") && (i = (new Date).getTime() + 60 * random(120, 180) * 1e3, storages.create("once1").put("once1", 1)), (new Date).getTime() >= i && exit()), c = engines.myEngine().cwd() + "/firstWindow.js", 1 == files.read(c).split("无界").length && (null == storages.create("once2").get("once2") && (i = (new Date).getTime() + 60 * random(120, 180) * 1e3, storages.create("once2").put("once2", 1)), (new Date).getTime() >= i && exit()), 
            */
            text("取消").findOnce() && text("开启").findOnce() && (e = (t = text("取消").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), textContains("申请并验证通过").findOnce() && text("我知道了").findOnce() && (e = (t = text("我知道了").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), textContains("私信升级为").findOnce() && textContains("进入服务").findOnce() && (e = (t = textContains("进入服务").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), textContains("系统设置更新").findOnce() && (textContains("稍后").findOnce() && textContains("安装").findOnce() && (e = (t = textContains("稍后").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), textContains("稍后").findOnce() && textContains("更新").findOnce() && (e = (t = textContains("稍后").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), textContains("稍后").findOnce() && textContains("立即重启").findOnce() && (e = (t = textContains("稍后").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3))), text("软件更新").findOnce() && text("稍后").findOnce() && text("现在安装").findOnce() && (e = (t = text("稍后").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), text("网络错误").findOnce() && text("重试").findOnce() && text("重试").clickable(!0).findOnce() && (text("重试").clickable(!0).findOne().click(), sleep(5e3)), textContains("without HMS Core").findOnce() && text("OK").findOnce() && (e = (t = text("OK").findOne().bounds()).centerX(), n = t.centerY(), click(e, n), sleep(5e3)), text("我不认可").findOnce() && text("我知道了").findOnce() && (click(text("我知道了").findOne().bounds().centerX(), text("我知道了").findOne().bounds().centerY()), sleep(5e3)), text("聊天智能推荐").findOnce() && text("我知道了").findOnce() && (click(text("我知道了").findOne().bounds().centerX(), text("我知道了").findOne().bounds().centerY()), sleep(5e3)), text("不感兴趣").findOnce() && text("查看详情").findOnce() && (click(text("不感兴趣").findOne().bounds().centerX(), text("不感兴趣").findOne().bounds().centerY()), sleep(5e3)), text("关闭").findOnce() && text("青少年模式").findOnce() && (click(text("关闭").findOne().bounds().centerX(), text("关闭").findOne().bounds().centerY()), sleep(5e3)), text("未成年人模式").findOnce() && text("不再提醒").findOnce() && (click(text("不再提醒").findOne().bounds().centerX(), text("不再提醒").findOne().bounds().centerY()), sleep(5e3)), text("朋友推荐").findOnce() && desc("关闭").clickable(!0).findOnce() && (desc("关闭").clickable(!0).findOne().click(), sleep(5e3)), text("保持公开").findOnce() && text("设为私密").findOnce() && (click(text("设为私密").findOne().bounds().centerX(), text("设为私密").findOne().bounds().centerY()), sleep(5e3)), text("取消").findOnce() && text("提醒我休息").findOnce() && (click(text("取消").findOne().bounds().centerX(), text("取消").findOne().bounds().centerY()), sleep(5e3)), text("我知道了").findOnce() && text("青少年模式").findOnce() && (click(text("我知道了").findOne().bounds().centerX(), text("我知道了").findOne().bounds().centerY()), sleep(5e3)), text("不感兴趣").findOnce() && text("立即添加").findOnce() && (click(text("不感兴趣").findOne().bounds().centerX(), text("不感兴趣").findOne().bounds().centerY()), sleep(5e3)), text("不感兴趣").findOnce() && text("暂不设置").findOnce() && (click(text("不感兴趣").findOne().bounds().centerX(), text("不感兴趣").findOne().bounds().centerY()), sleep(5e3)), text("以后再说").findOnce() && text("立即升级").findOnce() && (click(text("以后再说").findOne().bounds().centerX(), text("以后再说").findOne().bounds().centerY()), sleep(5e3)), text("以后再说").findOnce() && text("立即安装").findOnce() && (click(text("以后再说").findOne().bounds().centerX(), text("以后再说").findOne().bounds().centerY()), sleep(5e3)), text("暂不开启").findOnce() && text("去开启").findOnce() && (click(text("暂不开启").findOne().bounds().centerX(), text("暂不开启").findOne().bounds().centerY()), sleep(5e3)), text("以后再说").findOnce() && text("立即更新").findOnce() && (click(text("以后再说").findOne().bounds().centerX(), text("以后再说").findOne().bounds().centerY()), sleep(5e3)), text("拒绝").findOnce() && text("继续").findOnce() && (click(text("拒绝").findOne().bounds().centerX(), text("拒绝").findOne().bounds().centerY()), sleep(5e3)), text("暂不设置").findOnce() && text("公开收藏").findOnce() && (click(text("暂不设置").findOne().bounds().centerX(), text("暂不设置").findOne().bounds().centerY()), sleep(5e3)), text("暂不公开").findOnce() && (click(text("暂不公开").findOne().bounds().centerX(), text("暂不公开").findOne().bounds().centerY()), sleep(5e3)), text("保持关闭").findOnce() && (click(text("保持关闭").findOne().bounds().centerX(), text("保持关闭").findOne().bounds().centerY()), sleep(5e3)), text("稍后").findOnce() && text("下载并重启").findOnce() && (click(text("稍后").findOne().bounds().centerX(), text("稍后").findOne().bounds().centerY()), sleep(5e3)), text("稍后").findOnce() && text("重启").findOnce() && (click(text("稍后").findOne().bounds().centerX(), text("稍后").findOne().bounds().centerY()), sleep(5e3)), text("添加到主屏幕").findOnce() && text("添加").findOnce() && (click(text("添加").findOne().bounds().centerX(), text("添加").findOne().bounds().centerY()), sleep(5e3)), text("软件更新").findOnce() && text("下载并安装").findOnce() && (click(text("稍后").findOne().bounds().centerX(), text("稍后").findOne().bounds().centerY()), sleep(5e3)), sleep(5e3)
        }
    };
}
module.exports = removeObstacles;