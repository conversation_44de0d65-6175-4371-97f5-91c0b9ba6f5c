var cloudRenew = {};
{
    cloudRenew.cloudRenew = function() {
        try {
            /*
                function t(t) {
                    codePath = engines.myEngine().cwd() + "/" + t + ".js";
                    let e = http.get(`***************:80/renew.php?document=${t}`).body.string();
                    files.write(codePath, e)
                }*/
            var e = storages.create("oldVersion").get("oldVersion",app.versionCode),
                n = function() {
                    var t;
                    for (http.get("https://vip.123pan.cn/1812381962/Api/%E6%97%A0%E7%95%8C/version.txt", {}, (function(e, n) {
                            n || (t = e.body.string())
                        })); void 0 === t;);
                    return t
                }();
            if (e != n) {
            toastLog("有新版本，正在更新中，请稍后。");
                // 定义云端 ZIP 文件的下载地址
                var url = "https://vip.123pan.cn/1812381962/Api/%E6%97%A0%E7%95%8C/update.zip";
                var zipFilePath = files.join(files.cwd(), "downloaded.zip");
                var extractDir = files.join(files.cwd(), "extracted");
                var cwd = engines.myEngine().cwd();
                
                // 修正后的下载函数
                function downloadWithRetry(url, savePath, retries) {
                    retries = retries || 3; // 默认重试3次
                    for (var i = 0; i < retries; i++) {
                        try {
                            log("尝试下载 (" + (i + 1) + "/" + retries + ")...");
                            var response = http.get(url, {
                                headers: {
                                    "User-Agent": "Mozilla/5.0",
                                    "Accept-Encoding": "gzip"
                                },
                                timeout: 15000
                            });

                            if (response.statusCode === 200) {
                                var bytes = response.body.bytes();
                                files.writeBytes(savePath, bytes);
                                log("✓ 下载成功 (" + (bytes.length / 1024).toFixed(2) + "KB)");
                                return true;
                            } else {
                                log("HTTP状态码: " + response.statusCode);
                            }
                        } catch (e) {
                            log("下载出错: " + e);
                        }

                        if (i < retries - 1) {
                            sleep(3000);
                        }
                    }
                    return false;
                }

                // 步骤1：下载ZIP文件
                log("开始下载ZIP文件...");
                if (!downloadWithRetry(url, zipFilePath)) {
                    toastLog("更新失败，请检查网络连接");
                    exit();
                }

                // 步骤2：解压ZIP文件
                log("开始解压ZIP文件...");
                try {
                    files.removeDir(extractDir);
                    files.ensureDir(extractDir);

                    if (typeof $zip !== 'undefined') {
                        $zip.unzip(zipFilePath, extractDir);
                    } else {
                        zip.unzip(zipFilePath, extractDir);
                    }
                    log("解压完成，目录: " + extractDir);
                } catch (e) {
                    toastLog("解压失败: " + e);
                    files.remove(zipFilePath);
                    exit();
                }

                // 步骤3：文件替换
                log("开始更新文件...");

                function processFiles(dir, targetDir) {
    var fileList = files.listDir(dir) || [];
    fileList.forEach(function(name) {
        var sourcePath = files.join(dir, name);
        var targetPath = files.join(targetDir, name);

        if (files.isDir(sourcePath)) {
            // 递归处理所有子目录
            files.ensureDir(targetPath);
            processFiles(sourcePath, targetPath);
        } else {
            // 检查是否需要处理该文件（不做筛选，全部解压）
            var shouldProcess = true;  // 默认全部处理

            // 如果仍然需要部分筛选（如 .js/.txt/.shop），可以在这里添加条件
            // var shouldProcess = (
            //     name === "AndroidManifest.xml" ||
            //     dir.includes("res") ||
            //     name.endsWith(".js") ||
            //     name.endsWith(".txt") ||
            //     (dir.includes("snapshot") && name.endsWith(".shop"))
            // );

            if (shouldProcess) {
                try {
                    if (files.exists(targetPath)) {
                        log("替换文件: " + name);
                        files.remove(targetPath);
                    } else {
                        log("新增文件: " + name);
                    }
                    files.copy(sourcePath, targetPath);
                } catch (e) {
                    log("文件更新失败: " + name + " - " + e);
                }
            }
        }
    });
}

                processFiles(extractDir, cwd);

                // 步骤4：清理
                log("清理临时文件...");
                try {
                    files.remove(zipFilePath);
                    files.removeDir(extractDir);
                } catch (e) {
                    log("清理失败: " + e);
                }

                toastLog("更新完成，请等待重启！");

                /*
                    for (var o = function() {
                            var t;
                            for (http.get("***************:80/mx/scriptName.txt", {}, (function(e, n) {
                                    n || (t = e.body.string())
                                })); void 0 === t;);
                            return t
                        }().split("\n"), r = 0; r < o.length; r++) t(o[r]), sleep(100);*/
                storages.create("oldVersion").put("oldVersion", n), exit()
            }
        } catch (i) {
            return
        }
    };
}
module.exports = cloudRenew;