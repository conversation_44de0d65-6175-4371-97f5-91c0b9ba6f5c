<?xml version='1.0' encoding='utf-8' ?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:contentInsetStartWithNavigation="0dp">

            <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    
                    android:layout_gravity="center_vertical" />



                <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        
                        android:layout_marginEnd="16dp"
                        android:layout_gravity="center_vertical" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        
                        android:layout_gravity="center_vertical" />

                </LinearLayout>

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/contentScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/bannerViewPager"
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:layout_marginBottom="16dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="#FFFFFF"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardUseCompatPadding="true"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="通知"
                            android:textSize="18sp"
                            android:textColor="#333333"
                            android:textStyle="bold" />

                        <Space android:layout_width="0dp" android:layout_height="0dp" android:layout_weight="1" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            
                            android:tint="#999999" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="及时了解相关内容的最新动态，查看活跃下载的进度，并接收后台更新。"
                        android:textSize="14sp"
                        android:textColor="#666666" />

                    <Button
                        android:id="@+id/notifyButton"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_marginTop="16dp"
                        
                        android:text="授予权限"
                        android:textAllCaps="false"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="#FFFFFF"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="让这个应用程序更快"
                            android:textSize="18sp"
                            android:textColor="#333333"
                            android:textStyle="bold" />

                        <Space android:layout_width="0dp" android:layout_height="0dp" android:layout_weight="1" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            
                            android:tint="#999999" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="vivo（以及其他几家手机制造商）以意想不到的方式破坏有用的应用程序，只是为了人为地延长电池寿命。要恢复开发人员设计的该应用程序的全部性能，以下是一些提示。"
                        android:textSize="14sp"
                        android:textColor="#666666" />

                    <Button
                        android:id="@+id/learnMoreButton"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_marginTop="16dp"
                        
                        android:text="了解更多"
                        android:textAllCaps="false"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="轻应用"
                android:textSize="24sp"
                android:textColor="#333333"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <com.google.android.material.button.MaterialButton
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:layout_gravity="end"
                android:text="+ 新建"
                android:textSize="14sp"
                android:textAllCaps="false"
                android:backgroundTint="#E9ECEF"
                android:textColor="#333333"

                app:iconPadding="8dp"
                app:iconSize="18dp"
                app:cornerRadius="24dp"
                app:strokeColor="#CCCCCC"
                app:strokeWidth="1dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
