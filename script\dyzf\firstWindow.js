var firstWindow = {};
{
    firstWindow.firstWindow = function() {
        if (null != storages.create("oldVersion").get("oldVersion")) var e = "无界 " + storages.create("oldVersion").get("oldVersion") + "\n";
        else e = "";
        if (storages.create("en1").put("en1", "1"), null != storages.create("homeNickName").get("homeNickName")) var t = storages.create("homeNickName").get("homeNickName") + "\n";
        else t = "";
        if (storages.create("en2").put("en2", "2"), void 0 !== storages.create("jqfs").get(0)) var n = storages.create("jqfs").get(0) + "\n";
        else n = "";
        if (storages.create("en3").put("en3", "3"), null != storages.create("recordFansIncrease" + time()).get("recordFansIncrease" + time())) var o = "昨日增长 " + storages.create("recordFansIncrease" + time()).get("recordFansIncrease" + time()) + "\n";
        else o = "";
        if (storages.create("en4").put("en4", "4"), null != storages.create("jtzfds").get("jtzfds")) var r = "今日增长 " + storages.create("jtzfds").get("jtzfds") + "\n";
        else r = "";
        if (storages.create("en5").put("en5", "5"), storages.create("appName").get("appName")) {
            if ("抖音" == storages.create("appName").get("appName")) var i = "运行平台 正版\n";
            "com.ss.android.ugc.aweme.hubble" == storages.create("appName").get("appName") && (i = "运行平台 搜索\n"), "抖音商城" == storages.create("appName").get("appName") && (i = "运行平台 商城\n"), "抖音火山版" == storages.create("appName").get("appName") && (i = "运行平台 火山\n"), "抖音精选" == storages.create("appName").get("appName") && (i = "运行平台 精选\n"), "汽水音乐" == storages.create("appName").get("appName") && (i = "运行平台 汽水\n"), "多闪" == storages.create("appName").get("appName") && (i = "运行平台 多闪\n"), "com.ss.android.ugc.aweme.mobile" == storages.create("appName").get("appName") && (i = "运行平台 港版\n")
        } else i = "";
        if (storages.create("en").put("en", storages.create("en1").get("en1") + storages.create("en1").get("en1") + storages.create("en5").get("en5") + "." + storages.create("en1").get("en1") + storages.create("en2").get("en2") + "0." + storages.create("en2").get("en2") + storages.create("en4").get("en4") + storages.create("en3").get("en3") + "." + storages.create("en1").get("en1") + storages.create("en2").get("en2") + "8"), storages.create("convention").get("convention"))
            if (storages.create("liuhen").get("liuhen")) y = "";
            else {
                if (null != storages.create(time() + "arr").get(time() + "arr"))
                    if (null == storages.create("allTaskLength_convention").get("allTaskLength_convention")) var a = "";
                    else {
                        let e = storages.create("allTaskLength_convention").get("allTaskLength_convention");
                        //log("allTaskLength_convention"+e);
                        //log(storages.create(time() + "arr").get(time() + "arr").length);
                        a = "常规比例 " + (e - storages.create(time() + "arr").get(time() + "arr").length) + "/" + e + "\n"
                    }
                else a = "";
                if (storages.create("switchLike").get("switchLike"))
                    if (0 != storages.create(time() + "likeLength_convention").get(time() + "likeLength_convention") && null != storages.create(time() + "likeLength_convention").get(time() + "likeLength_convention")) {
                        var c = storages.create(time() + "likeLength_convention").get(time() + "likeLength_convention");
                        if (null != storages.create(time() + "recordLikeCount_convention").get(time() + "recordLikeCount_convention")) var s = storages.create(time() + "recordLikeCount_convention").get(time() + "recordLikeCount_convention");
                        else s = 0;
                        var g = "点赞   " + s + "/" + c + "\n"
                    } else g = "";
                else g = "";
                if (storages.create("switchImageText").get("switchImageText"))
                    if (0 != storages.create(time() + "imageTextLength_convention").get(time() + "imageTextLength_convention") && null != storages.create(time() + "imageTextLength_convention").get(time() + "imageTextLength_convention")) {
                        var m = storages.create(time() + "imageTextLength_convention").get(time() + "imageTextLength_convention");
                        if (null != storages.create(time() + "recordImageTextCount_convention").get(time() + "recordImageTextCount_convention")) var l = storages.create(time() + "recordImageTextCount_convention").get(time() + "recordImageTextCount_convention");
                        else l = 0;
                        var v = "图文   " + l + "/" + m + "\n"
                    } else v = "";
                else v = "";
                if (storages.create("switchProfilePicture").get("switchProfilePicture"))
                    if (0 != storages.create(time() + "profilePictureLength_convention").get(time() + "profilePictureLength_convention") && null != storages.create(time() + "profilePictureLength_convention").get(time() + "profilePictureLength_convention")) {
                        var h = storages.create(time() + "profilePictureLength_convention").get(time() + "profilePictureLength_convention");
                        if (null != storages.create(time() + "recordProfilePictureCount_convention").get(time() + "recordProfilePictureCount_convention")) var u = storages.create(time() + "recordProfilePictureCount_convention").get(time() + "recordProfilePictureCount_convention");
                        else u = 0;
                        var _ = "头像   " + u + "/" + h + "\n"
                    } else _ = "";
                else _ = "";
                if (storages.create("switchCollect").get("switchCollect"))
                    if (0 != storages.create(time() + "collectLength_convention").get(time() + "collectLength_convention") && null != storages.create(time() + "collectLength_convention").get(time() + "collectLength_convention")) {
                        var L = storages.create(time() + "collectLength_convention").get(time() + "collectLength_convention");
                        if (null != storages.create(time() + "recordCollectCount_convention").get(time() + "recordCollectCount_convention")) var f = storages.create(time() + "recordCollectCount_convention").get(time() + "recordCollectCount_convention");
                        else f = 0;
                        var C = "收藏   " + f + "/" + L + "\n"
                    } else C = "";
                else C = "";
                if (storages.create("switchComment").get("switchComment"))
                    if (0 != storages.create(time() + "commentLength_convention").get(time() + "commentLength_convention") && null != storages.create(time() + "commentLength_convention").get(time() + "commentLength_convention")) {
                        var d = storages.create(time() + "commentLength_convention").get(time() + "commentLength_convention");
                        if (null != storages.create(time() + "recordCommentCount_convention").get(time() + "recordCommentCount_convention")) var p = storages.create(time() + "recordCommentCount_convention").get(time() + "recordCommentCount_convention");
                        else p = 0;
                        var w = "评论   " + p + "/" + d + "\n"
                    } else w = "";
                else w = "";
                if (storages.create("switchFollowWithInterest").get("switchFollowWithInterest"))
                    if (0 != storages.create(time() + "followWithInterestLength_convention").get(time() + "followWithInterestLength_convention") && null != storages.create(time() + "followWithInterestLength_convention").get(time() + "followWithInterestLength_convention")) {
                        var k = storages.create(time() + "followWithInterestLength_convention").get(time() + "followWithInterestLength_convention");
                        if (null != storages.create(time() + "recordFollowWithInterestCount_convention").get(time() + "recordFollowWithInterestCount_convention")) var x = storages.create(time() + "recordFollowWithInterestCount_convention").get(time() + "recordFollowWithInterestCount_convention");
                        else x = 0;
                        var N = "关注   " + x + "/" + k + "\n"
                    } else N = "";
                else N = "";
                if (storages.create("switchPrivateLetter").get("switchPrivateLetter"))
                    if (0 != storages.create(time() + "privateLetterLength_convention").get(time() + "privateLetterLength_convention") && null != storages.create(time() + "privateLetterLength_convention").get(time() + "privateLetterLength_convention")) {
                        var P = storages.create(time() + "privateLetterLength_convention").get(time() + "privateLetterLength_convention");
                        if (null != storages.create(time() + "recordPrivateLetterCount_convention").get(time() + "recordPrivateLetterCount_convention")) var I = storages.create(time() + "recordPrivateLetterCount_convention").get(time() + "recordPrivateLetterCount_convention");
                        else I = 0;
                        var T = "私信   " + I + "/" + P + "\n"
                    } else T = "";
                else T = "";
                if (storages.create("switchShare").get("switchShare"))
                    if (0 != storages.create(time() + "shareLength_convention").get(time() + "shareLength_convention") && null != storages.create(time() + "shareLength_convention").get(time() + "shareLength_convention")) {
                        var W = storages.create(time() + "shareLength_convention").get(time() + "shareLength_convention");
                        if (null != storages.create(time() + "recordShareCount_convention").get(time() + "recordShareCount_convention")) var F = storages.create(time() + "recordShareCount_convention").get(time() + "recordShareCount_convention");
                        else F = 0;
                        var b = "分享   " + F + "/" + W + "\n"
                    } else b = "";
                else b = "";
                if (storages.create("switchClickComment").get("switchClickComment"))
                    if (0 != storages.create(time() + "clickCommentLength_convention").get(time() + "clickCommentLength_convention") && null != storages.create(time() + "clickCommentLength_convention").get(time() + "clickCommentLength_convention")) {
                        var j = storages.create(time() + "clickCommentLength_convention").get(time() + "clickCommentLength_convention");
                        if (null != storages.create(time() + "recordClickCommentCount_convention").get(time() + "recordClickCommentCount_convention")) var D = storages.create(time() + "recordClickCommentCount_convention").get(time() + "recordClickCommentCount_convention");
                        else D = 0;
                        var S = "点评   " + D + "/" + j + "\n"
                    } else S = "";
                else S = "";
                var y = a + g + v + _ + C + w + N + T + b + S
            }
        else y = "";
        if (storages.create("rest").get("rest"))
            if (null != storages.create("meiyunxingxiuxi").get("meiyunxingxiuxi")) var z = function(e) {
                var t = new Date(e).getHours();
                t < 10 && (t = "0" + t);
                var n = new Date(e).getMinutes();
                n < 10 && (n = "0" + n);
                var o = new Date(e).getSeconds();
                return o < 10 && (o = "0" + o), t + ":" + n + ":" + o
            }(storages.create("meiyunxingxiuxi").get("meiyunxingxiuxi")) + ` 休息${Number(ui.xiuxi.getText())}分钟\n`;
            else z = "";
        else z = "";
        /*
        var V = engines.myEngine().cwd() + "/convention.js";
       
        if (files.read(V).length != storages.create("number").get("number").split("x")[0]) {
            if (null == storages.create("once3").get("once3")) {
                var q = (new Date).getTime() + 60 * random(120, 180) * 1e3;
                storages.create("once3").put("once3", 1)
            }(new Date).getTime() >= q && exit()
        }
        V = engines.myEngine().cwd() + "/removeObstacles.js", files.read(V).length != storages.create("number").get("number").split("x")[1] && (null == storages.create("once4").get("once4") && (q = (new Date).getTime() + 60 * random(120, 180) * 1e3, storages.create("once4").put("once4", 1)), (new Date).getTime() >= q && exit());
        */
        var E = e + t + n + o + r + i + y + z;
        return "\n" == E.charAt(E.length - 1) && (E = E.slice(0, E.length - 1)), E
    };
}
module.exports = firstWindow;