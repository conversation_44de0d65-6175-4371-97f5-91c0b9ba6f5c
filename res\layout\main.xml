<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 顶部工具栏（替代原AppBarLayout）android:background="#6200EE"  -->
<androidx.appcompat.widget.Toolbar
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="#6200EE"
    app:titleTextColor="#FFFFFF" 
    app:popupTheme="@style/ScriptTheme.PopupOverlay"
    app:title="无界"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"/>

    <!-- 中间ViewPager（位于工具栏下方，底部导航上方） -->
    <com.stardust.autojs.core.ui.widget.JsViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent" 
        android:layout_height="0dp"  
        app:layout_constraintTop_toBottomOf="@id/toolbar"  
        app:layout_constraintLeft_toLeftOf="parent" 
        app:layout_constraintRight_toRightOf="parent"  
        app:layout_constraintBottom_toTopOf="@id/navigation"/>  

    <!-- 底部导航栏（固定在底部） -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:background="?android:attr/windowBackground"
        app:menu="@menu/navigation"
        app:layout_constraintBottom_toBottomOf="parent" 
        app:layout_constraintLeft_toLeftOf="parent" 
        app:layout_constraintRight_toRightOf="parent"/> 

</androidx.constraintlayout.widget.ConstraintLayout>