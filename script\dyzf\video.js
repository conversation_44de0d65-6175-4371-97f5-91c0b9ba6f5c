var video = {};
{
    (() => { var e = function (e) { for (var n = (new Date).getTime(); !((new Date).getTime() - n > e);); }; video.video = function () { !function (n) { function t(e) { importClass(android.content.Intent), importClass(android.net.Uri); var n = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS); n.setData(Uri.parse("package:" + e)), n.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK), app.startActivity(n) } for (device.wakeUp(); ;) { for (storages.create("countNone").put("countNone", 0); !(text("强行停止").findOnce() || text("结束运行").findOnce() || text("强制停止").findOnce());) { if (home(), 1 == storages.create("delay").get("delay")) for (var i = random(5, 180); i > 0; i--)log(i + "秒后开始"), e(1e3); else e(5e3); click(device.width / 2, device.height / 1.1), e(5e3), t(app.getPackageName(n)), e(5e3), (text("移除").findOnce() || desc("移除").findOnce()) && (text("移除").findOnce() ? (click(text("移除").findOne().bounds().centerX(), text("移除").findOne().bounds().centerY()), e(3e3)) : desc("移除").findOnce() && (click(desc("移除").findOne().bounds().centerX(), desc("移除").findOne().bounds().centerY()), e(3e3)), text("移除").findOnce() ? (click(text("移除").findOne().bounds().centerX(), text("移除").findOne().bounds().centerY()), e(3e3)) : desc("移除").findOnce() && (click(desc("移除").findOne().bounds().centerX(), desc("移除").findOne().bounds().centerY()), e(3e3)), t(app.getPackageName(n)), e(5e3)), text("要卸载吗？").findOnce() && (text("取消").clickable(!0).findOne().click(), e(5e3)) } if (text("强行停止").findOnce() && (text("强行停止").findOnce() && (click(text("强行停止").findOne().bounds().centerX(), text("强行停止").findOne().bounds().centerY()), e(2e3)), text("确定").findOnce() ? (click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), e(2e3)) : text("强行停止").findOnce() && (click(text("强行停止").findOne().bounds().centerX(), text("强行停止").findOne().bounds().centerY()), e(2e3))), text("结束运行").findOnce() && (click(text("结束运行").findOne().bounds().centerX(), text("结束运行").findOne().bounds().centerY()), e(2e3), text("确定").findOnce() && (click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), e(2e3)), text("清除数据").findOnce() && (click(text("清除数据").findOne().bounds().centerX(), text("清除数据").findOne().bounds().centerY()), e(2e3), click(text("清除缓存").findOne().bounds().centerX(), text("清除缓存").findOne().bounds().centerY()), e(2e3), click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), e(2e3))), text("强制停止").findOnce() && (text("强制停止").findOnce() && (click(text("强制停止").findOne().bounds().centerX(), text("强制停止").findOne().bounds().centerY()), e(2e3)), text("确定").findOnce() ? (click(text("确定").findOne().bounds().centerX(), text("确定").findOne().bounds().centerY()), e(2e3)) : text("强制停止").findOnce() && (click(text("强制停止").findOne().bounds().centerX(), text("强制停止").findOne().bounds().centerY()), e(2e3))), back(), e(5e3), launchApp(n), e(5e3), text("允许").findOnce() && text("拒绝").findOnce() ? (click(text("允许").findOne().bounds().centerX(), text("允许").findOne().bounds().centerY()), e(3e3)) : text("打开").findOnce() && text("取消").findOnce() && (click(text("打开").findOne().bounds().centerX(), text("打开").findOne().bounds().centerY()), e(3e3)), storages.create("qishui").get("qishui")) for (var c = 0; ;) { if (text("发现").findOnce() && text("我的").findOnce()) return; if (c++, e(1e3), c >= 30) break } else for (c = 0; ;) { if (text("消息").findOnce() && text("我").findOnce()) return; if (c++, e(1e3), c >= 30) break } } }("抖音"); var n = (new Date).getTime(), t = random(20, 50), i = n + 60 * t * 1e3; log("养号" + t + "分钟"), e(5e3), swipe(device.width / 2, device.height / 1.3, device.width / 2, 0, 100), e(1e3); for (var c = 0; ;) { if ((new Date).getTime() >= i) { log("时间到了"), home(), e(5e3), storages.create("recordLastViewingTime").put("recordLastViewingTime", (new Date).getTime() + 60 * random(30, 180) * 1e3); break } var d = random(1e3, 8e3); log("观看" + d + "毫秒"), e(d); var r = descStartsWith("未点赞").descEndsWith("按钮").findOnce(0); if (r) { var f = r.desc(); c <= 20 && f.split("万").length > 1 && f.split("喜欢")[1].split("万")[0] <= 15 && (log("执行点赞"), descStartsWith("未点赞").descEndsWith("按钮").findOnce(0).click(), e(1e3), c++, log("已点赞" + c + "次")) } log("翻页"), swipe(device.width / 2, device.height / 1.3, device.width / 2, 0, 100), e(1e3) } } })();
}
module.exports = video;